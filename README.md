# Qyi Games Studio Portfolio Website

A modern, glassmorphism-style portfolio website for Qyi Games Studio, an independent Arabic game development studio specializing in Unreal Engine 5 games.

## Features

- **Modern Glassmorphism Design**: Transparent cards with blur effects and elegant styling
- **Responsive Layout**: Mobile-first design that works on all devices
- **Smooth Animations**: CSS and JavaScript animations for enhanced user experience
- **Video Background**: Hero section with looping video background
- **Interactive Elements**: Hover effects, animated counters, and smooth scrolling
- **Arabic Support**: Bilingual content with Arabic text support
- **Performance Optimized**: Throttled scroll events and efficient animations

## Sections

1. **Hero Section**: Studio name, tagline, and call-to-action buttons
2. **About Section**: Studio description and statistics
3. **Projects Section**: Game showcases with Steam links
4. **Technologies Section**: Tools and platforms used
5. **Contact Section**: Social media links and contact information

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS with glassmorphism effects
├── script.js           # JavaScript for interactivity
├── assets/
│   ├── images/         # Game screenshots and images
│   └── videos/         # Background videos and trailers
└── README.md           # This file
```

## Customization

### Adding Your Own Content

1. **Replace Placeholder Images**: Add your game screenshots to `assets/images/` and update the `src` attributes in `index.html`

2. **Add Background Video**: Replace the video URL in the hero section with your own video file:
   ```html
   <source src="assets/videos/your-background-video.mp4" type="video/mp4">
   ```

3. **Update Game Projects**: Modify the projects section with your actual games:
   - Replace project titles, descriptions, and images
   - Update Steam links to your actual game pages
   - Add appropriate tags for each game

4. **Social Media Links**: Update the contact section with your actual social media profiles:
   ```html
   <a href="https://twitter.com/yourstudio" class="social-icon"><i class="fab fa-twitter"></i></a>
   ```

5. **Contact Information**: Update email and location in the contact section

### Color Scheme

The website uses a gradient color scheme that can be customized in `styles.css`:

- Primary gradient: `#667eea` to `#764ba2`
- Accent colors: `#ff6b6b` and `#4ecdc4`
- Text: White with various opacity levels

### Fonts

The website uses the Inter font family from Google Fonts. You can change this by updating the import in `index.html` and the font-family in `styles.css`.

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance Tips

1. **Optimize Images**: Compress your game screenshots and use WebP format when possible
2. **Video Optimization**: Use compressed MP4 videos for backgrounds
3. **Lazy Loading**: Consider adding lazy loading for images below the fold
4. **CDN**: Host assets on a CDN for faster loading times

## Development

To run the website locally:

1. Clone or download the files
2. Open `index.html` in a web browser
3. For development, use a local server like Live Server in VS Code

## Deployment

The website is built with vanilla HTML, CSS, and JavaScript, making it easy to deploy on any web hosting service:

- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Netlify**: Drag and drop the folder to Netlify
- **Vercel**: Connect your repository for automatic deployments
- **Traditional Hosting**: Upload files via FTP to any web host

## License

This template is free to use for personal and commercial projects. Attribution is appreciated but not required.

## Credits

- Icons: Font Awesome
- Fonts: Google Fonts (Inter)
- Images: Unsplash (placeholder images)
- Design: Custom glassmorphism implementation

## Support

For questions or customization help, feel free to reach out or create an issue in the repository.

---

**Made with ❤️ for the gaming community**
